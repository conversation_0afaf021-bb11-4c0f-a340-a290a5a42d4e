2025-04-02 17:06:06,715 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-02 17:06:09,761 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-02 17:06:23,578 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-02 17:06:46,275 - INFO - Process request initiated with action: tutela_correo_contestacion
2025-04-02 17:07:00,500 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 14.23s
2025-04-02 17:08:08,259 - INFO - Process request initiated with action: tutela_contestacion
2025-04-02 17:08:10,709 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 2.45s
2025-04-02 17:08:15,167 - INFO - Process request initiated with action: tutela_fallo
2025-04-02 17:08:17,292 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 2.13s
2025-04-02 17:08:23,186 - INFO - Process request initiated with action: tutela_desacato
2025-04-02 17:08:25,570 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 2.39s
2025-04-04 11:26:35,095 - INFO - Process request initiated with action: cv
2025-04-04 11:26:51,700 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 16.61s
2025-04-04 11:34:20,517 - INFO - Process request initiated with action: cv
2025-04-04 11:35:09,854 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 49.34s
2025-04-04 11:39:14,741 - INFO - Process request initiated with action: cv
2025-04-04 11:39:44,076 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 29.34s
2025-04-04 11:50:50,342 - INFO - Process request initiated with action: cv
2025-04-04 11:51:16,731 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 26.39s
2025-04-04 12:02:56,600 - INFO - Process request initiated with action: cv
2025-04-04 12:03:05,501 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 8.90s
2025-04-08 15:35:36,747 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-08 15:36:01,342 - INFO - Process request initiated with action: invoice_test
2025-04-08 15:36:25,276 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-08 15:37:30,933 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 89.59s
2025-04-08 15:38:11,703 - INFO - Process request initiated with action: invoice
2025-04-08 15:39:53,499 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 101.79s
2025-04-08 16:24:38,145 - INFO - Process request initiated with action: invoice_test
2025-04-08 16:26:09,592 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 91.45s
2025-04-08 17:18:56,438 - INFO - Process request initiated with action: invoice
2025-04-08 17:20:17,913 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 81.48s
2025-04-08 17:21:18,532 - INFO - Process request initiated with action: invoice
2025-04-08 17:21:22,252 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-08 17:22:29,613 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 71.08s
2025-04-09 09:04:01,250 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.03s
2025-04-09 09:20:57,962 - INFO - Process request initiated with action: invoice
2025-04-09 09:21:03,250 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-09 09:32:48,331 - INFO - Process request initiated with action: invoice
2025-04-09 09:35:29,296 - INFO - Process request initiated with action: invoice
2025-04-09 09:39:40,254 - INFO - Process request initiated with action: cv
2025-04-09 09:41:43,455 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 500 - Time: 123.20s
2025-04-09 11:34:50,097 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-09 11:35:03,229 - INFO - Process request initiated with action: invoice
2025-04-09 11:37:44,257 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 161.03s
2025-04-09 13:02:20,612 - INFO - Process request initiated with action: invoice
2025-04-09 13:04:52,235 - INFO - Task invoice-**********.6133254-2459782776304 (invoice) completed in 151.62s
2025-04-09 13:04:52,236 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 151.63s
2025-04-09 13:05:03,574 - INFO - Process request initiated with action: invoice
2025-04-09 13:05:07,759 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-09 13:05:17,329 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.02s
2025-04-09 13:05:27,096 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-09 13:05:30,873 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-09 13:08:08,870 - INFO - Task invoice-**********.5744038-2459792277184 (invoice) completed in 185.30s
2025-04-09 13:08:08,871 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 185.30s
2025-04-09 13:35:58,793 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-09 13:36:03,436 - INFO - Process request initiated with action: invoice
2025-04-09 13:38:31,753 - INFO - Task invoice-**********.4363651-2072511264768 (invoice) completed in 148.32s
2025-04-09 13:38:31,754 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 148.32s
2025-04-09 13:40:56,050 - INFO - Process request initiated with action: tutela_contestacion
2025-04-09 13:40:58,232 - INFO - Task tutela_contestacion-**********.0507507-2072511437312 (tutela_contestacion) completed in 2.18s
2025-04-09 13:40:58,233 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 2.19s
2025-04-09 13:41:11,827 - INFO - Process request initiated with action: tutela_fallo
2025-04-09 13:41:14,022 - INFO - Task tutela_fallo-**********.8272023-2072515158976 (tutela_fallo) completed in 2.20s
2025-04-09 13:41:14,022 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 2.20s
2025-04-09 13:41:19,465 - INFO - Process request initiated with action: tutela_desacato
2025-04-09 13:41:21,510 - INFO - Task tutela_desacato-1744224079.4653974-2072515527360 (tutela_desacato) completed in 2.04s
2025-04-09 13:41:21,511 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 2.05s
2025-04-09 13:41:33,677 - INFO - Process request initiated with action: tutela_correo
2025-04-09 13:41:46,937 - INFO - Task tutela_correo-**********.678713-2072521363904 (tutela_correo) completed in 13.26s
2025-04-09 13:41:46,938 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 13.26s
2025-04-09 13:42:17,390 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-11 09:16:26,688 - INFO - Process request initiated with action: tutela_correo
2025-04-11 09:16:42,402 - INFO - Task tutela_correo-**********.6899734-1815567133792 (tutela_correo) completed in 15.71s
2025-04-11 09:16:42,403 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 15.72s
2025-04-11 09:18:41,766 - INFO - Process request initiated with action: invoice
2025-04-11 09:19:08,949 - INFO - Task invoice-**********.7660933-1815572274816 (invoice) completed in 27.18s
2025-04-11 09:19:08,950 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 27.19s
2025-04-11 09:24:58,542 - INFO - Process request initiated with action: invoice
2025-04-11 09:25:17,364 - INFO - Task invoice-**********.542507-1815567029584 (invoice) completed in 18.82s
2025-04-11 09:25:17,365 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 18.82s
2025-04-14 07:44:45,734 - INFO - Process request initiated with action: invoice
2025-04-14 07:44:45,736 - ERROR - Error processing action: 500: Error when processing the invoice: 400: The file must be a PDF or image.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 221, in process_invoice
    raise HTTPException(status_code=400, detail="The file must be a PDF or image.")
fastapi.exceptions.HTTPException: 400: The file must be a PDF or image.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\routes\process.py", line 95, in handle_request_with_semaphore
    result = await processor.process(file, data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 84, in process
    result = await self.process_invoice(file, data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 240, in process_invoice
    raise HTTPException(status_code=500, detail=f"Error when processing the invoice: {str(e)}")
fastapi.exceptions.HTTPException: 500: Error when processing the invoice: 400: The file must be a PDF or image.
2025-04-14 07:44:45,738 - INFO - Task invoice-1744634685.7346063-2460639472496 (invoice) completed in 0.00s
2025-04-14 07:44:45,738 - ERROR - Task invoice-1744634685.7346063-2460639472496 failed with error: 500: Error processing the request: 500: Error when processing the invoice: 400: The file must be a PDF or image.
2025-04-14 07:44:45,738 - ERROR - Unexpected error in task invoice-1744634685.7346063-2460639472496: 500: Error processing the request: 500: Error when processing the invoice: 400: The file must be a PDF or image.
2025-04-14 07:44:45,738 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 500 - Time: 0.01s
2025-04-14 08:19:09,660 - INFO - Process request initiated with action: invoice
2025-04-14 08:19:21,956 - INFO - Task invoice-1744636749.6606126-2833935002336 (invoice) completed in 12.30s
2025-04-14 08:19:21,957 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 12.30s
2025-04-14 08:29:23,518 - INFO - Process request initiated with action: invoice
2025-04-14 08:30:03,836 - INFO - Task invoice-1744637363.5187006-2013304153552 (invoice) completed in 40.32s
2025-04-14 08:30:03,837 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 40.32s
2025-04-14 08:33:24,952 - INFO - Process request initiated with action: invoice
2025-04-14 08:33:32,656 - INFO - Task invoice-**********.9525208-2014170001536 (invoice) completed in 7.70s
2025-04-14 08:33:32,656 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 7.71s
2025-04-14 13:03:44,242 - INFO - Request: GET http://localhost:8000/api/v1/opportunities/cached/ - Status: 404 - Time: 0.00s
2025-04-14 13:03:44,285 - INFO - Request: GET http://localhost:8000/favicon.ico - Status: 404 - Time: 0.00s
2025-04-14 14:27:39,391 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.02s
2025-04-14 14:27:47,105 - INFO - Process request initiated with action: invoice
2025-04-14 14:28:02,156 - ERROR - Error processing action: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 261, in process_invoice
    page_data = response['response']
                ~~~~~~~~^^^^^^^^^^^^
TypeError: 'JSONResponse' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 270, in process_invoice
    raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")
fastapi.exceptions.HTTPException: 500: Error processing image: 'JSONResponse' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\routes\process.py", line 95, in handle_request_with_semaphore
    result = await processor.process(file, data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 139, in process
    result = await self.process_invoice(file, data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 385, in process_invoice
    raise HTTPException(status_code=500, detail=f"Error when processing the invoice: {str(e)}")
fastapi.exceptions.HTTPException: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
2025-04-14 14:28:02,188 - INFO - Task invoice-1744658867.106394-2496361394640 (invoice) completed in 15.08s
2025-04-14 14:28:02,188 - ERROR - Task invoice-1744658867.106394-2496361394640 failed with error: 500: Error processing the request: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
2025-04-14 14:28:02,188 - ERROR - Unexpected error in task invoice-1744658867.106394-2496361394640: 500: Error processing the request: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
2025-04-14 14:28:02,188 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 500 - Time: 15.08s
2025-04-14 14:30:04,114 - INFO - Process request initiated with action: invoice
2025-04-14 14:30:19,153 - ERROR - Error processing action: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 261, in process_invoice
    page_data = response['response']
                ~~~~~~~~^^^^^^^^^^^^
TypeError: 'JSONResponse' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 270, in process_invoice
    raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")
fastapi.exceptions.HTTPException: 500: Error processing image: 'JSONResponse' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\routes\process.py", line 95, in handle_request_with_semaphore
    result = await processor.process(file, data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 139, in process
    result = await self.process_invoice(file, data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 385, in process_invoice
    raise HTTPException(status_code=500, detail=f"Error when processing the invoice: {str(e)}")
fastapi.exceptions.HTTPException: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
2025-04-14 14:30:19,155 - INFO - Task invoice-1744659004.1153946-2486127256944 (invoice) completed in 15.04s
2025-04-14 14:30:19,155 - ERROR - Task invoice-1744659004.1153946-2486127256944 failed with error: 500: Error processing the request: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
2025-04-14 14:30:19,155 - ERROR - Unexpected error in task invoice-1744659004.1153946-2486127256944: 500: Error processing the request: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
2025-04-14 14:30:19,156 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 500 - Time: 15.04s
2025-04-14 14:34:05,358 - INFO - Process request initiated with action: invoice
2025-04-14 14:34:20,371 - ERROR - Error processing action: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 261, in process_invoice
    page_data = response['response']
                ~~~~~~~~^^^^^^^^^^^^
TypeError: 'JSONResponse' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 270, in process_invoice
    raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")
fastapi.exceptions.HTTPException: 500: Error processing image: 'JSONResponse' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\routes\process.py", line 95, in handle_request_with_semaphore
    result = await processor.process(file, data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 139, in process
    result = await self.process_invoice(file, data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\VSC\job\LumusAzure\LumusAI\services\processors\facturius\invoice_processor_test.py", line 385, in process_invoice
    raise HTTPException(status_code=500, detail=f"Error when processing the invoice: {str(e)}")
fastapi.exceptions.HTTPException: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
2025-04-14 14:34:20,374 - INFO - Task invoice-1744659245.3592448-2539116031600 (invoice) completed in 15.01s
2025-04-14 14:34:20,374 - ERROR - Task invoice-1744659245.3592448-2539116031600 failed with error: 500: Error processing the request: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
2025-04-14 14:34:20,374 - ERROR - Unexpected error in task invoice-1744659245.3592448-2539116031600: 500: Error processing the request: 500: Error when processing the invoice: 500: Error processing image: 'JSONResponse' object is not subscriptable
2025-04-14 14:34:20,374 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 500 - Time: 15.02s
2025-04-14 15:45:07,712 - INFO - Process request initiated with action: invoice
2025-04-14 15:45:15,157 - INFO - Task invoice-**********.7125177-2378706134576 (invoice) completed in 7.45s
2025-04-14 15:45:15,158 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 7.45s
2025-04-14 17:01:56,381 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-14 17:02:06,251 - INFO - Process request initiated with action: invoice
2025-04-14 17:02:10,404 - INFO - Task invoice-**********.251581-2629666848304 (invoice) completed in 4.15s
2025-04-14 17:02:10,404 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 4.16s
2025-04-14 17:03:00,374 - INFO - Process request initiated with action: cv
2025-04-14 17:03:34,343 - INFO - Task cv-**********.374556-2629671508864 (cv) completed in 33.97s
2025-04-14 17:03:34,343 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 33.97s
2025-04-14 17:07:32,226 - INFO - Process request initiated with action: cv
2025-04-14 17:08:05,686 - INFO - Task cv-**********.2262008-2042926950992 (cv) completed in 33.46s
2025-04-14 17:08:05,687 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 33.46s
2025-04-14 17:11:46,256 - INFO - Process request initiated with action: cv
2025-04-14 17:12:13,019 - INFO - Task cv-1744668706.2569234-2078339246816 (cv) completed in 26.76s
2025-04-14 17:12:13,019 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 26.76s
2025-04-14 17:22:16,649 - INFO - Process request initiated with action: cv
2025-04-14 17:22:46,851 - INFO - Task cv-1744669336.6496031-2056421928800 (cv) completed in 30.20s
2025-04-14 17:22:46,852 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 30.21s
2025-04-14 17:24:38,569 - INFO - Process request initiated with action: cv
2025-04-14 17:27:02,838 - INFO - Task cv-1744669478.569306-2056454408480 (cv) completed in 144.27s
2025-04-14 17:27:02,840 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 144.28s
2025-04-16 15:03:18,366 - INFO - Process request initiated with action: cv
2025-04-16 15:05:36,493 - INFO - Task cv-1744833798.366939-2848427488560 (cv) completed in 138.13s
2025-04-16 15:05:36,495 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 138.13s
2025-04-21 17:08:09,706 - INFO - Process request initiated with action: invoice
2025-04-21 17:08:31,365 - INFO - Task invoice-1745273289.7083511-3007315892832 (invoice) completed in 21.66s
2025-04-21 17:08:31,366 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 21.67s
2025-04-21 17:09:20,710 - INFO - Process request initiated with action: invoice
2025-04-21 17:09:43,088 - INFO - Task invoice-1745273360.7108343-3007349639984 (invoice) completed in 22.38s
2025-04-21 17:09:43,088 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 22.38s
2025-04-21 17:10:22,600 - INFO - Process request initiated with action: invoice
2025-04-21 17:10:46,129 - INFO - Task invoice-1745273422.600856-3007325049952 (invoice) completed in 23.53s
2025-04-21 17:10:46,129 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 23.53s
2025-04-21 17:12:08,306 - INFO - Process request initiated with action: invoice
2025-04-21 17:12:34,552 - INFO - Task invoice-1745273528.3067677-3007325123072 (invoice) completed in 26.25s
2025-04-21 17:12:34,552 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 26.25s
2025-04-21 17:17:50,444 - INFO - Process request initiated with action: invoice
2025-04-21 17:18:17,121 - INFO - Task invoice-1745273870.4440665-2502147517872 (invoice) completed in 26.68s
2025-04-21 17:18:17,122 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 26.68s
2025-04-21 17:19:34,990 - INFO - Process request initiated with action: invoice
2025-04-21 17:20:00,957 - INFO - Task invoice-1745273974.9908922-2124139196256 (invoice) completed in 25.97s
2025-04-21 17:20:00,958 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 25.97s
2025-04-21 17:21:29,036 - INFO - Process request initiated with action: invoice
2025-04-21 17:21:58,452 - INFO - Task invoice-1745274089.0366569-2124984102752 (invoice) completed in 29.42s
2025-04-21 17:21:58,453 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 29.42s
2025-04-21 17:24:56,646 - INFO - Process request initiated with action: invoice
2025-04-21 17:25:15,173 - INFO - Task invoice-1745274296.6468694-1797485866464 (invoice) completed in 18.53s
2025-04-21 17:25:15,174 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 18.53s
2025-04-22 09:20:14,746 - INFO - Process request initiated with action: cv
2025-04-22 09:20:16,209 - INFO - Task cv-1745331614.7469718-2366983926176 (cv) completed in 1.46s
2025-04-22 09:20:16,210 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 1.47s
2025-04-22 09:30:47,927 - INFO - Process request initiated with action: cv
2025-04-22 09:32:02,869 - INFO - Task cv-1745332247.9284108-1820115905440 (cv) completed in 74.94s
2025-04-22 09:32:02,870 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 74.94s
2025-04-29 13:14:02,320 - INFO - Process request initiated with action: invoice
2025-04-29 13:15:26,199 - INFO - Task invoice-1745950442.3209622-2106540762480 (invoice) completed in 83.88s
2025-04-29 13:15:26,199 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 83.88s
2025-04-29 13:16:13,549 - INFO - Process request initiated with action: invoice
2025-04-29 13:17:44,641 - INFO - Task invoice-1745950573.5503232-2303157968272 (invoice) completed in 91.09s
2025-04-29 13:17:44,642 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 91.10s
2025-04-29 13:22:54,873 - INFO - Process request initiated with action: invoice
2025-04-29 13:23:33,345 - INFO - Task invoice-1745950974.8734891-2303158202784 (invoice) completed in 38.47s
2025-04-29 13:23:33,346 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 38.47s
2025-04-29 13:25:21,808 - INFO - Process request initiated with action: invoice
2025-04-29 13:25:43,418 - INFO - Task invoice-**********.808317-2303158206096 (invoice) completed in 21.61s
2025-04-29 13:25:43,419 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 21.61s
2025-04-29 13:28:38,299 - INFO - Process request initiated with action: invoice
2025-04-29 13:28:50,274 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:28:58,868 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:29:02,865 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:29:05,148 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:29:08,102 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:29:10,288 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:29:12,720 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:29:14,311 - INFO - Task invoice-**********.3003306-2774280198272 (invoice) completed in 36.01s
2025-04-29 13:29:14,312 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 36.01s
2025-04-29 13:29:18,335 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:30:26,316 - INFO - Process request initiated with action: invoice
2025-04-29 13:30:52,671 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:30:55,288 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:30:57,405 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:30:59,650 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 13:31:07,843 - INFO - Task invoice-**********.316517-2773424725008 (invoice) completed in 41.53s
2025-04-29 13:31:07,844 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 41.54s
2025-04-29 13:31:09,017 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.01s
2025-04-29 16:57:11,753 - INFO - Process request initiated with action: invoice
2025-04-29 16:57:20,609 - INFO - Request: GET http://127.0.0.1:8000/health - Status: 200 - Time: 1.02s
2025-04-29 16:57:33,192 - INFO - Task invoice-**********.7541654-2556009697920 (invoice) completed in 21.44s
2025-04-29 16:57:33,193 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 21.45s
2025-05-06 08:40:51,144 - INFO - Process request initiated with action: invoice
2025-05-06 08:40:58,789 - INFO - Task invoice-**********.1445942-2389912128304 (invoice) completed in 7.64s
2025-05-06 08:40:58,789 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 7.65s
2025-05-15 13:59:59,191 - INFO - Process request initiated with action: cv
2025-05-15 14:00:51,287 - INFO - Task cv-**********.1919737-1960099137296 (cv) completed in 52.10s
2025-05-15 14:00:51,288 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 52.10s
2025-05-15 14:10:06,970 - INFO - Process request initiated with action: cv
2025-05-15 14:11:10,966 - INFO - Task cv-**********.9710577-2592263294080 (cv) completed in 64.00s
2025-05-15 14:11:10,967 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 64.00s
2025-05-15 15:12:04,533 - INFO - Process request initiated with action: cv
2025-05-15 15:13:04,190 - INFO - Task cv-1747339924.5337126-2348588878880 (cv) completed in 59.66s
2025-05-15 15:13:04,192 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 59.66s
2025-05-15 15:43:55,180 - INFO - Process request initiated with action: cv
2025-05-15 15:44:55,383 - INFO - Task cv-1747341835.1810336-2262029767200 (cv) completed in 60.20s
2025-05-15 15:44:55,384 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 60.21s
2025-05-15 16:15:15,571 - INFO - Process request initiated with action: cv
2025-05-15 16:16:21,837 - INFO - Task cv-1747343715.5722046-1406621373200 (cv) completed in 66.27s
2025-05-15 16:16:21,838 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 66.27s
2025-05-15 16:30:45,505 - INFO - Process request initiated with action: cv
2025-05-15 16:31:36,313 - INFO - Task cv-1747344645.5058734-1554147855536 (cv) completed in 50.81s
2025-05-15 16:31:36,314 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 50.81s
2025-05-16 00:28:14,573 - INFO - Process request initiated with action: cv
2025-05-16 00:28:14,573 - WARNING - No file or data provided.
2025-05-16 00:28:14,574 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 400 - Time: 0.00s
2025-05-16 00:28:17,991 - INFO - Process request initiated with action: cv
2025-05-16 00:28:17,992 - WARNING - No file or data provided.
2025-05-16 00:28:17,992 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 400 - Time: 0.00s
2025-05-16 00:29:50,887 - INFO - Process request initiated with action: cv
2025-05-16 00:30:15,754 - INFO - Task cv-1747373390.8870826-2328871364720 (cv) completed in 24.87s
2025-05-16 00:30:15,754 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 24.87s
2025-05-16 00:30:35,243 - INFO - Process request initiated with action: cv
2025-05-16 00:31:33,395 - INFO - Task cv-1747373435.2434783-2328881652480 (cv) completed in 58.15s
2025-05-16 00:31:33,396 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 58.15s
2025-05-16 01:40:16,651 - INFO - Request: POST http://localhost:8000/process/cv - Status: 404 - Time: 0.00s
2025-05-16 01:41:11,573 - INFO - Process request initiated with action: cv
2025-05-16 01:43:07,694 - INFO - Task cv-1747377671.5733888-1606301831856 (cv) completed in 116.12s
2025-05-16 01:43:07,694 - INFO - Request: POST http://localhost:8000/process - Status: 503 - Time: 116.12s
2025-05-16 01:44:46,675 - INFO - Process request initiated with action: cv
2025-05-16 01:46:42,774 - INFO - Task cv-1747377886.6757472-1604171227088 (cv) completed in 116.10s
2025-05-16 01:46:42,774 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 503 - Time: 116.11s
2025-05-16 01:49:08,798 - INFO - Process request initiated with action: cv
2025-05-16 01:49:49,095 - WARNING - Task was cancelled during processing
2025-05-16 01:49:49,095 - INFO - Task cv-1747378148.7983954-1604172159776 (cv) completed in 40.30s
2025-05-16 01:49:49,116 - WARNING - Task cv-1747378148.7983954-1604172159776 was cancelled
2025-05-16 01:50:36,343 - INFO - Process request initiated with action: cv
2025-05-16 01:52:03,766 - INFO - Process request initiated with action: invoice
2025-05-16 01:52:37,832 - INFO - Task invoice-1747378323.7660608-1952968331808 (invoice) completed in 34.07s
2025-05-16 01:52:37,832 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 34.07s
2025-05-16 01:53:10,937 - INFO - Process request initiated with action: cv
2025-05-16 01:54:07,932 - INFO - Task cv-**********.9378204-1953822977168 (cv) completed in 56.99s
2025-05-16 01:54:07,933 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 57.00s
2025-05-16 01:54:31,806 - INFO - Request: GET http://localhost:8000/health - Status: 200 - Time: 1.01s
2025-05-16 01:54:38,330 - INFO - Process request initiated with action: cv
2025-05-16 01:55:35,056 - INFO - Task cv-**********.3308713-1953829169648 (cv) completed in 56.73s
2025-05-16 01:55:35,057 - INFO - Request: POST http://localhost:8000/process - Status: 200 - Time: 56.73s
2025-05-16 02:00:40,768 - INFO - Process request initiated with action: cv
2025-05-16 02:08:31,546 - INFO - Process request initiated with action: cv
2025-05-16 02:09:20,511 - INFO - Task cv-**********.5466828-2354056992608 (cv) completed in 48.96s
2025-05-16 02:09:20,512 - INFO - Request: POST http://localhost:8000/process - Status: 200 - Time: 48.97s
2025-05-16 02:10:53,151 - INFO - Process request initiated with action: cv
2025-05-16 02:11:49,195 - INFO - Task cv-**********.1520743-2422602518064 (cv) completed in 56.04s
2025-05-16 02:11:49,197 - INFO - Request: POST http://localhost:8000/process - Status: 200 - Time: 56.05s
2025-05-16 02:14:54,717 - INFO - Process request initiated with action: cv
2025-05-16 02:15:47,886 - INFO - Task cv-1747379694.7173767-2733008262192 (cv) completed in 53.17s
2025-05-16 02:15:47,887 - INFO - Request: POST http://localhost:8000/process - Status: 200 - Time: 53.17s
2025-05-16 02:15:49,055 - INFO - Process request initiated with action: cv
2025-05-16 02:16:49,701 - INFO - Task cv-1747379749.055736-2733012329744 (cv) completed in 60.64s
2025-05-16 02:16:49,702 - INFO - Request: POST http://localhost:8000/process - Status: 200 - Time: 60.65s
2025-05-16 02:18:32,821 - INFO - Process request initiated with action: cv
2025-05-16 02:19:41,555 - INFO - Task cv-1747379912.8216476-2733016927888 (cv) completed in 68.73s
2025-05-16 02:19:41,556 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 68.74s
2025-05-16 02:32:39,685 - INFO - Process request initiated with action: cv
2025-05-16 02:33:40,686 - INFO - Task cv-1747380759.6858342-2758240081088 (cv) completed in 61.00s
2025-05-16 02:33:40,687 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 61.00s
2025-05-16 02:34:02,004 - INFO - Process request initiated with action: cv
2025-05-16 02:35:01,135 - INFO - Task cv-1747380842.0049274-2758247911200 (cv) completed in 59.13s
2025-05-16 02:35:01,136 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 59.13s
2025-05-16 02:35:19,539 - INFO - Process request initiated with action: cv
2025-05-16 02:36:24,296 - INFO - Task cv-1747380919.5402672-2758248711376 (cv) completed in 64.76s
2025-05-16 02:36:24,297 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 64.76s
2025-05-16 02:38:37,946 - INFO - Process request initiated with action: cv
2025-05-16 02:39:13,166 - WARNING - Task was cancelled during processing
2025-05-16 02:39:13,166 - INFO - Task cv-1747381117.9465907-1736063011728 (cv) completed in 35.22s
2025-05-16 02:39:13,187 - WARNING - Task cv-1747381117.9465907-1736063011728 was cancelled
2025-05-16 02:39:41,976 - INFO - Process request initiated with action: cv
2025-05-16 02:42:01,734 - INFO - Task cv-1747381181.9765067-2130329799616 (cv) completed in 139.76s
2025-05-16 02:42:01,735 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 139.76s
2025-05-16 02:43:40,707 - INFO - Process request initiated with action: cv
2025-05-16 02:44:41,315 - INFO - Task cv-1747381420.7082825-1282063174400 (cv) completed in 60.61s
2025-05-16 02:44:41,316 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 60.61s
2025-05-16 02:57:20,471 - INFO - Process request initiated with action: cv
2025-05-16 02:58:36,445 - INFO - Task cv-1747382240.471381-2315279650496 (cv) completed in 75.97s
2025-05-16 02:58:36,446 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 75.98s
2025-05-20 06:11:02,009 - INFO - Process request initiated with action: cv
2025-05-20 06:12:06,781 - INFO - Task cv-**********.0101774-2699270868624 (cv) completed in 64.77s
2025-05-20 06:12:06,782 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 64.77s
2025-05-20 06:21:29,633 - INFO - Process request initiated with action: cv
2025-05-20 06:22:34,770 - INFO - Task cv-**********.6335382-2344592100096 (cv) completed in 65.14s
2025-05-20 06:22:34,771 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 65.14s
2025-05-20 06:41:31,410 - INFO - Request: GET http://127.0.0.1:8001/health - Status: 200 - Time: 1.01s
2025-05-20 06:42:11,948 - INFO - Request: GET http://127.0.0.1:8001/health - Status: 200 - Time: 1.01s
2025-05-20 06:42:17,671 - INFO - Process request initiated with action: cv
2025-05-20 06:43:06,735 - INFO - Task cv-**********.6720452-2119102602304 (cv) completed in 49.06s
2025-05-20 06:43:06,736 - INFO - Request: POST http://127.0.0.1:8001/process - Status: 200 - Time: 49.07s
2025-05-20 15:11:03,256 - INFO - Request: GET http://127.0.0.1:8001/health - Status: 200 - Time: 1.03s
2025-05-20 16:34:41,001 - INFO - Request: GET http://127.0.0.1:8001/health - Status: 200 - Time: 1.01s
2025-05-26 06:43:57,845 - INFO - Process request initiated with action: invoice
2025-05-26 06:44:21,803 - INFO - Task invoice-**********.8456008-2230588769968 (invoice) completed in 23.96s
2025-05-26 06:44:21,804 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 23.96s
2025-05-26 06:45:29,496 - INFO - Process request initiated with action: invoice
2025-05-26 06:46:05,665 - INFO - Task invoice-**********.4967296-2229297674960 (invoice) completed in 36.17s
2025-05-26 06:46:05,665 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 36.17s
2025-05-26 06:46:43,049 - INFO - Process request initiated with action: invoice
2025-05-26 06:47:42,443 - INFO - Task invoice-**********.0495942-2230588779280 (invoice) completed in 59.39s
2025-05-26 06:47:42,444 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 59.40s
2025-05-26 07:02:47,450 - INFO - Process request initiated with action: invoice
2025-05-26 07:03:07,946 - INFO - Task invoice-1748260967.4513915-1323915074048 (invoice) completed in 20.49s
2025-05-26 07:03:07,946 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 20.50s
2025-05-26 08:24:25,578 - INFO - Process request initiated with action: invoice
2025-05-26 08:24:46,248 - INFO - Task invoice-1748265865.5791035-1323915216704 (invoice) completed in 20.67s
2025-05-26 08:24:46,249 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 20.68s
2025-05-26 08:30:18,910 - INFO - Process request initiated with action: invoice
2025-05-26 08:30:37,311 - INFO - Task invoice-1748266218.9101102-1935745529952 (invoice) completed in 18.40s
2025-05-26 08:30:37,312 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 18.40s
2025-05-26 08:36:31,588 - INFO - Process request initiated with action: invoice
2025-05-26 08:36:50,797 - INFO - Task invoice-1748266591.588911-1790590954976 (invoice) completed in 19.21s
2025-05-26 08:36:50,798 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 19.21s
2025-05-26 08:51:21,454 - INFO - Process request initiated with action: invoice
2025-05-26 08:51:52,448 - INFO - Task invoice-1748267481.4542227-1301168950928 (invoice) completed in 30.99s
2025-05-26 08:51:52,449 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 31.00s
2025-05-26 08:56:15,128 - INFO - Process request initiated with action: invoice
2025-05-26 08:57:44,887 - INFO - Task invoice-1748267775.1284134-1301169797888 (invoice) completed in 89.76s
2025-05-26 08:57:44,888 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 89.76s
2025-05-26 09:03:06,124 - INFO - Process request initiated with action: invoice
2025-05-26 09:04:15,042 - INFO - Task invoice-1748268186.1245863-1564760891392 (invoice) completed in 68.92s
2025-05-26 09:04:15,044 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 68.94s
2025-05-26 09:05:12,882 - INFO - Process request initiated with action: invoice
2025-05-26 09:06:35,927 - INFO - Task invoice-1748268312.8833756-2298490816896 (invoice) completed in 83.04s
2025-05-26 09:06:35,928 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 83.05s
2025-05-26 09:22:04,179 - INFO - Process request initiated with action: cv
2025-05-26 09:23:15,204 - INFO - Task cv-1748269324.1790078-2185447476640 (cv) completed in 71.03s
2025-05-26 09:23:15,205 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 71.03s
2025-05-26 09:23:27,367 - INFO - Process request initiated with action: cv
2025-05-26 09:24:44,340 - INFO - Task cv-1748269407.3673203-2184161552784 (cv) completed in 76.97s
2025-05-26 09:24:44,341 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 76.98s
2025-05-27 08:40:41,878 - INFO - Process request initiated with action: invoice
2025-05-27 08:40:48,869 - INFO - Task invoice-1748353241.8785505-1513188511712 (invoice) completed in 6.99s
2025-05-27 08:40:48,870 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 6.99s
2025-05-27 08:41:46,226 - INFO - Process request initiated with action: invoice
2025-05-27 08:41:52,402 - INFO - Task invoice-1748353306.2267919-1511896401200 (invoice) completed in 6.18s
2025-05-27 08:41:52,402 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 6.18s
2025-05-27 08:42:49,903 - INFO - Process request initiated with action: invoice
2025-05-27 08:42:56,930 - INFO - Task invoice-1748353369.9031284-1513188512048 (invoice) completed in 7.03s
2025-05-27 08:42:56,931 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 7.03s
2025-05-27 08:43:10,315 - INFO - Process request initiated with action: invoice
2025-05-27 08:43:18,193 - INFO - Task invoice-1748353390.3155973-1511896595488 (invoice) completed in 7.88s
2025-05-27 08:43:18,194 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 7.88s
2025-05-27 08:43:37,112 - INFO - Process request initiated with action: invoice
2025-05-27 08:43:43,697 - INFO - Task invoice-1748353417.1128256-1511896582192 (invoice) completed in 6.58s
2025-05-27 08:43:43,697 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 6.59s
2025-05-27 10:59:01,405 - INFO - Process request initiated with action: invoice
2025-05-27 10:59:58,717 - INFO - Task invoice-1748361541.4073226-1511896414400 (invoice) completed in 57.31s
2025-05-27 10:59:58,718 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 57.33s
2025-05-27 11:00:34,072 - INFO - Process request initiated with action: invoice
2025-05-27 11:01:33,993 - INFO - Task invoice-1748361634.0730965-1511900264160 (invoice) completed in 59.92s
2025-05-27 11:01:33,994 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 59.92s
2025-05-27 17:09:57,470 - INFO - Process request initiated with action: invoice
2025-05-27 17:11:10,244 - INFO - Task invoice-1748383797.4776685-1511900257632 (invoice) completed in 72.77s
2025-05-27 17:11:10,246 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 72.81s
2025-05-30 05:22:05,093 - INFO - Process request initiated with action: invoice
2025-05-30 05:22:28,083 - INFO - Task invoice-1748600525.0939162-1813299220640 (invoice) completed in 22.99s
2025-05-30 05:22:28,083 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 22.99s
2025-05-30 05:23:06,661 - INFO - Process request initiated with action: cv
2025-05-30 05:24:03,872 - INFO - Task cv-1748600586.662448-1814590341632 (cv) completed in 57.21s
2025-05-30 05:24:03,872 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 57.21s
2025-05-30 05:27:39,931 - INFO - Process request initiated with action: cv
2025-05-30 05:28:41,020 - INFO - Task cv-1748600859.932265-1813302899920 (cv) completed in 61.09s
2025-05-30 05:28:41,021 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 61.09s
2025-05-30 05:35:11,717 - INFO - Process request initiated with action: cv
2025-05-30 05:36:29,563 - INFO - Task cv-1748601311.718298-2846510568112 (cv) completed in 77.85s
2025-05-30 05:36:29,564 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 77.85s
2025-05-30 05:43:48,187 - INFO - Process request initiated with action: cv
2025-05-30 05:44:52,771 - INFO - Task cv-1748601828.187797-2847637049648 (cv) completed in 64.58s
2025-05-30 05:44:52,771 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 64.59s
2025-05-30 05:53:16,234 - INFO - Process request initiated with action: cv
2025-05-30 05:54:05,416 - INFO - Task cv-1748602396.23488-2743188279888 (cv) completed in 49.18s
2025-05-30 05:54:05,417 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 49.19s
2025-05-30 05:58:49,767 - INFO - Process request initiated with action: cv
2025-05-30 05:59:50,344 - INFO - Task cv-1748602729.7676778-3025496101120 (cv) completed in 60.58s
2025-05-30 05:59:50,345 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 60.58s
2025-05-30 06:03:53,965 - INFO - Process request initiated with action: cv
2025-05-30 06:04:53,336 - INFO - Task cv-1748603033.9658422-2518163575808 (cv) completed in 59.37s
2025-05-30 06:04:53,338 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 59.38s
2025-05-30 06:08:27,797 - INFO - Process request initiated with action: cv
2025-05-30 06:09:17,164 - INFO - Task cv-1748603307.797984-2963777918576 (cv) completed in 49.37s
2025-05-30 06:09:17,165 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 49.37s
2025-05-30 06:17:19,446 - INFO - Process request initiated with action: cv
2025-05-30 06:18:10,341 - INFO - Task cv-1748603839.4461882-2248834717584 (cv) completed in 50.90s
2025-05-30 06:18:10,342 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 50.90s
2025-05-30 09:04:20,382 - INFO - Process request initiated with action: cv
2025-05-30 09:05:27,871 - INFO - Task cv-1748613860.3825483-1249894028624 (cv) completed in 67.49s
2025-05-30 09:05:27,872 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 67.49s
2025-05-30 09:09:29,514 - INFO - Process request initiated with action: cv
2025-05-30 09:10:19,792 - INFO - Task cv-1748614169.5143023-1812823135808 (cv) completed in 50.28s
2025-05-30 09:10:19,793 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 50.28s
2025-05-30 09:17:22,718 - INFO - Process request initiated with action: cv
2025-05-30 09:18:15,027 - INFO - Task cv-1748614642.718416-2188327539712 (cv) completed in 52.31s
2025-05-30 09:18:15,029 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 52.31s
2025-05-30 14:46:26,272 - INFO - Process request initiated with action: cv
2025-05-30 14:47:15,170 - INFO - Task cv-1748634386.273557-2115409795680 (cv) completed in 48.90s
2025-05-30 14:47:15,171 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 48.91s
2025-05-30 14:59:04,612 - INFO - Process request initiated with action: cv
2025-05-30 14:59:52,649 - INFO - Task cv-1748635144.6126282-2530761299184 (cv) completed in 48.04s
2025-05-30 14:59:52,650 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 48.04s
2025-06-11 06:11:32,712 - INFO - Process request initiated with action: invoice
2025-06-11 06:11:40,526 - INFO - Task invoice-1749640292.7131088-2389048135648 (invoice) completed in 7.81s
2025-06-11 06:11:40,526 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 7.82s
2025-06-11 06:26:37,858 - INFO - Process request initiated with action: invoice
2025-06-11 06:26:45,675 - INFO - Task invoice-1749641197.8583922-1695429743952 (invoice) completed in 7.82s
2025-06-11 06:26:45,675 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 7.82s
2025-06-13 06:53:05,032 - INFO - Process request initiated with action: cv
2025-06-13 06:53:33,988 - INFO - Task cv-1749815585.03262-2701009615808 (cv) completed in 28.96s
2025-06-13 06:53:33,990 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 28.97s
2025-06-13 06:55:38,542 - INFO - Process request initiated with action: cv
2025-06-13 06:56:03,763 - INFO - Task cv-1749815738.542409-2699723145728 (cv) completed in 25.22s
2025-06-13 06:56:03,765 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 25.24s
2025-06-13 12:49:02,190 - INFO - Process request initiated with action: cv
2025-06-13 12:55:23,423 - INFO - Task cv-1749836942.19177-2701010444432 (cv) completed in 381.23s
2025-06-13 12:55:23,424 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 381.24s
2025-06-19 11:45:48,273 - INFO - Process request initiated with action: cv
2025-06-19 11:52:29,101 - INFO - Task cv-1750351548.274186-2105730567968 (cv) completed in 400.83s
2025-06-19 11:52:29,103 - INFO - Request: POST http://127.0.0.1:8000/process - Status: 200 - Time: 400.83s
