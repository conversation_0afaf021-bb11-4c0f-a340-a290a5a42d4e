# System Architecture Document
## LumusAI - Intelligent Document Processing Service

**Document Version:** 1.0  
**Date:** December 2024  
**Architect:** Development Team  
**Project:** LumusAI  

---

## Table of Contents

1. [Introduction](#1-introduction)
2. [Architectural Overview](#2-architectural-overview)
3. [System Components](#3-system-components)
4. [Data Architecture](#4-data-architecture)
5. [Integration Architecture](#5-integration-architecture)
6. [Deployment Architecture](#6-deployment-architecture)
7. [Security Architecture](#7-security-architecture)
8. [Performance Considerations](#8-performance-considerations)

---

## 1. Introduction

### 1.1 Purpose
This document describes the system architecture for LumusAI, an intelligent document processing service. It provides a comprehensive view of the system's structure, components, and their interactions.

### 1.2 Scope
The architecture covers:
- High-level system design
- Component architecture and responsibilities
- Data flow and processing patterns
- Integration points and external dependencies
- Deployment and infrastructure considerations

### 1.3 Architectural Goals
- **Modularity:** Clear separation of concerns between processing modules
- **Scalability:** Support for concurrent processing and horizontal scaling
- **Reliability:** Robust error handling and recovery mechanisms
- **Maintainability:** Clean code structure and comprehensive logging
- **Performance:** Efficient processing with configurable concurrency

## 2. Architectural Overview

### 2.1 System Context

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   Web Browser   │    │  Integration    │
│                 │    │                 │    │   Systems       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │        LumusAI            │
                    │   Document Processing     │
                    │        Service            │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
┌─────────▼───────┐    ┌─────────▼───────┐    ┌─────────▼───────┐
│   OpenAI API    │    │  Document Files │    │   Monitoring    │
│                 │    │                 │    │    Systems      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 Architectural Patterns

**Microservice Architecture:**
- Single-responsibility services for each document type
- RESTful API interfaces
- Independent deployment and scaling

**Layered Architecture:**
- Presentation Layer (FastAPI endpoints)
- Business Logic Layer (Document processors)
- Integration Layer (AI services, utilities)
- Infrastructure Layer (Logging, monitoring)

**Event-Driven Processing:**
- Asynchronous task processing
- Concurrent document handling
- Task queue management

## 3. System Components

### 3.1 Core Components

#### 3.1.1 API Gateway Layer
**Component:** FastAPI Application (`main.py`)
- **Responsibilities:**
  - HTTP request handling
  - CORS configuration
  - Request/response logging
  - Error handling and status codes
  - Middleware processing

**Key Features:**
- Automatic API documentation (Swagger/OpenAPI)
- Request validation and serialization
- Global exception handling
- Performance monitoring

#### 3.1.2 Routing Layer
**Component:** Route Handlers (`routes/`)
- **Process Router:** Document processing endpoints
- **Health Router:** System monitoring endpoints
- **Maintenance Router:** Task management and cleanup

**Responsibilities:**
- Request routing and parameter validation
- Task orchestration and concurrency control
- Response formatting and error handling

#### 3.1.3 Processing Engine
**Component:** Document Processors (`services/processors/`)

**SmartHR Module:**
- CV/Resume text extraction and analysis
- Personal information parsing
- Work experience duration calculation
- Skills categorization and aggregation

**Papirus Module:**
- Legal document text extraction
- Tutela document type identification
- Legal entity and case information extraction
- Email communication processing

**Facturius Module:**
- Invoice data extraction and validation
- Multi-format support (PDF, Excel, images)
- Line item parsing and total calculation
- Utility bill processing

#### 3.1.4 AI Integration Layer
**Component:** LangChain Client (`utils/langchain_client.py`)
- **Responsibilities:**
  - OpenAI/Azure OpenAI API integration
  - Prompt engineering and model interaction
  - Response validation and retry logic
  - Token usage tracking and cost calculation

**Features:**
- Structured data extraction using Pydantic models
- Image and text processing capabilities
- Automatic retry mechanisms
- Error handling and fallback strategies

#### 3.1.5 Utility Services
**Component:** Helper Functions (`utils/`)
- **Document Processing:** PDF, DOCX, Excel file handling
- **Text Extraction:** OCR and content parsing
- **Data Validation:** Input sanitization and format checking
- **File Management:** Temporary file handling and cleanup

### 3.2 Data Models

#### 3.2.1 Domain Models (`domain/models/`)
**SmartHR Models:**
- `CV`: Complete curriculum vitae structure
- `PersonalInfo`: Contact and personal details
- `WorkExperience`: Employment history and responsibilities
- `Education`: Academic background and qualifications
- `Skills`: Technical and soft skills with experience levels

**Papirus Models:**
- `TutelaContestacionDocument`: Legal response documents
- `TutelaFalloDocument`: Legal ruling documents
- `CorreoTutela`: Email communication structures

**Facturius Models:**
- `InvoiceBase`: Common invoice properties
- `InvoicePurchase`: Purchase invoice specifics
- `InvoiceElectric/Water/Gas`: Utility bill structures
- `ProductItem`: Line item details
- `TotalBill`: Billing summary information

### 3.3 Infrastructure Components

#### 3.3.1 Concurrency Management
- **Semaphore-based task limiting**
- **Active task registry and monitoring**
- **Graceful task cancellation and cleanup**

#### 3.3.2 Logging and Monitoring
- **Structured logging with configurable levels**
- **Request/response tracking**
- **Performance metrics collection**
- **Error tracking and alerting**

#### 3.3.3 Health Monitoring
- **System resource monitoring (CPU, memory)**
- **Task queue status and metrics**
- **API dependency health checks**
- **Maintenance and cleanup operations**

## 4. Data Architecture

### 4.1 Data Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Document  │───▶│   Upload/   │───▶│  Document   │───▶│ Structured  │
│   Input     │    │   Validate  │    │ Processing  │    │   Output    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                           │                   │
                           ▼                   ▼
                   ┌─────────────┐    ┌─────────────┐
                   │   Error     │    │   AI Model  │
                   │  Handling   │    │ Integration │
                   └─────────────┘    └─────────────┘
```

### 4.2 Data Processing Pipeline

1. **Input Validation:** File format and size validation
2. **Text Extraction:** Convert documents to processable text
3. **AI Processing:** Send to language models for analysis
4. **Data Structuring:** Parse AI responses into Pydantic models
5. **Validation:** Ensure data quality and completeness
6. **Response Generation:** Format and return structured JSON

### 4.3 Data Formats

**Input Formats:**
- PDF documents
- Microsoft Word (DOCX) files
- Excel spreadsheets (XLS, XLSX)
- Image files (PNG, JPG, JPEG)
- Plain text files

**Output Format:**
- JSON with structured data models
- Consistent error response format
- Metadata including processing costs and timing

## 5. Integration Architecture

### 5.1 External Integrations

#### 5.1.1 OpenAI/Azure OpenAI API
- **Purpose:** AI-powered document analysis
- **Protocol:** HTTPS REST API
- **Authentication:** API key-based
- **Data Exchange:** JSON requests/responses with base64 images

#### 5.1.2 Client Applications
- **Interface:** RESTful HTTP API
- **Data Format:** JSON request/response
- **Authentication:** Configurable (currently open)
- **CORS:** Enabled for cross-origin requests

### 5.2 Internal Integration Patterns

#### 5.2.1 Processor Pattern
- Common interface for all document processors
- Standardized process() method signature
- Consistent error handling and response format

#### 5.2.2 Factory Pattern
- Dynamic processor selection based on action type
- Centralized processor instantiation
- Dependency injection for shared services

## 6. Deployment Architecture

### 6.1 Containerization

**Docker Configuration:**
- Base image: Python 3.12.7-alpine
- Multi-stage build for optimization
- Dependency management with requirements.txt
- Port exposure: 8000

**Container Features:**
- Minimal attack surface with Alpine Linux
- Optimized layer caching
- Health check endpoints
- Graceful shutdown handling

### 6.2 Environment Configuration

**Required Environment Variables:**
- `API_KEY`: OpenAI/Azure API authentication
- `API_VERSION`: API version specification
- `AZURE_ENDPOINT`: Azure OpenAI endpoint URL
- `MODEL`: AI model identifier

**Optional Configuration:**
- `MAX_CONCURRENT_TASKS`: Concurrency limit (default: 4)
- `ROOT_PATH`: Application root path for reverse proxy

### 6.3 Scaling Considerations

**Horizontal Scaling:**
- Stateless application design
- Load balancer compatibility
- Shared configuration via environment variables

**Vertical Scaling:**
- Configurable concurrency limits
- Memory optimization for large documents
- CPU utilization monitoring

## 7. Security Architecture

### 7.1 Input Security
- File type validation and sanitization
- Size limits for uploaded documents
- Content validation before processing

### 7.2 API Security
- Input validation using Pydantic models
- Error message sanitization
- Request logging for audit trails

### 7.3 External Service Security
- Secure API key management
- HTTPS communication with external services
- Timeout and retry configurations

## 8. Performance Considerations

### 8.1 Processing Optimization
- Asynchronous processing with asyncio
- Concurrent task execution with semaphores
- Efficient memory management and cleanup

### 8.2 Response Time Optimization
- Streaming responses for large documents
- Caching strategies for repeated requests
- Connection pooling for external APIs

### 8.3 Resource Management
- Configurable concurrency limits
- Memory monitoring and garbage collection
- Temporary file cleanup

---

**Document Control:**
- **Version:** 1.0
- **Status:** Final
- **Last Updated:** December 2024
- **Next Review:** Upon architectural changes
