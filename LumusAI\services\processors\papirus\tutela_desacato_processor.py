from services.processors.base_processor import DocumentProcessor
from utils.openai_client import OpenAIClient
from utils.langchain_client import LangChainClient
from fastapi import UploadFile, HTTPException
from typing import Optional
from pydantic import BaseModel, ValidationError
import os
import tempfile
from urllib.request import urlopen
from io import BytesIO
from PyPDF2 import PdfReader
import time
import re
import json
from fastapi.responses import JSONResponse
import requests
import fitz
import base64
from domain.models.papirus.tutela_desacato_model import TutelaDesacatoDocument
from utils.helpers import get_text_from_pdf
import asyncio  # Used for CancelledError handling

text_ex1 = """


JUZGADO 124 PENAL MUNICIPAL FUNCIÓN
CONOCIMIENTO DE BOGOTÁ D.C.
<EMAIL>




Página 1 de 3

JUZGADO 124 PENAL MUNICIPAL CON FUNCION DE
CONOCIMIENTO DE BOGOTÁ D.C.

Radicación: 110014 ************ 00261 00
Accionante: SOL KATHERINE NOREÑA ARIAS
Accionado: BANCOLOMBIA S.A.
Asunto: INCIDENTE DESACATO
Ciudad y fecha: Bogotá D.C., diciembre doce (12) de dos mil veinticuatro
(2024)


El veintinueve (29) de noviembre de 2024 se recibió, en el correo electrónico
de este Despacho, escrito de solicitud de apertura de incidente de desacato,
suscrito por SOL KATHERINE NOREÑA ARIAS en contra del Representante
Legal de BANCOLOMBIA S.A. por no dar cumplimiento al fallo de tutela
proferido por esta instancia judicial el día veinticinco (25) de noviembre de
2024.

Al respecto, es de indicar que en el referido fallo de tutela se resolvió,

“PRIMERO: AMPARAR el derecho fundamental de petición invocado por
SOL KATHERINE NOREÑA ARIAS , y en consecuencia ORDENAR a
BANCOLOMBIA S.A. que, si no lo ha hecho, proceda a emitir en el término
de 48 horas contadas a partir de la notificación del presente fallo, una
respuesta de fondo a la solicitud elevada el 25 de septiembre de 2024 por
la accionante”.

En ese sentido, el día 2 de diciembre de 2024, se ef ectuó requerimiento
previo a la accionada , acorde a lo dispuesto en el artículo 27 del Decreto
2591 de 1991, a fin de que informara  al despacho judicial sobre el
cumplimiento de lo dispuesto en el fallo de tutela
110014009124202400261.

El 5 de diciembre  de 2024, Manuela Gómez Giraldo , en calidad de
apoderada judicial de BANCOLOMBIA S.A., señaló, “ante el llamado judicial
efectuado, se le informa al Juzgado que la solicitud presentada por la
accionante fue resuelta en comunicación del 05 de noviembre de 2024 la cual
fue enviada al correo: <EMAIL>. En dicha comunicación
se atendió la solicitud de la accionante de forma clara precisa y de fondo.

Para efectos de brindar claridad respecto al cumplimiento de la orden
constitucional procederé a relacionar las respuestas brindadas a la
accionante, las cuales en su conjunto resuelven las peticiones realizadas”.

Debe recordarse que, de acuerdo con su formulación jurídica, el incidente
de desacato ha sido entendido como un procedimiento: (i) que se inscribe en
el ejercicio del poder jurisdiccional sancionatorio; (ii) cuyo trámite tiene
carácter incidental.

JUZGADO 124 PENAL MUNICIPAL FUNCIÓN
CONOCIMIENTO DE BOGOTÁ D.C.
<EMAIL>




Página 2 de 3


La Corte Constitucional ha manifestado que la sanción que puede ser
impuesta dentro del incidente de desacato tiene carácter disciplinario,
dentro de los rangos de multa y arresto, resaltando que, si bien entre los
objetivos del incidente de desacato está sancionar el incumplimiento del
fallo de tutela por parte de la autoridad responsable, ciertamente lo que se
busca lograr es el cumplimiento efectivo de la orden de tutela pendiente de
ser ejecutada y por ende, la materialización de la protección de los derechos
fundamentales con ella amparados.

Así, la jurisprudencia constitucional ha señalado:

“...En el evento de presentarse el desconocimiento de una orden proferida por
el juez constitucional, el sistema jurídico tiene prevista una oportunidad y una
vía procesal específica, con el fin de obtener que las sentencias de tutela se
cumplan y, para que, en caso de no sean obedecidas, se impongan sanciones
que pueden ser pecuniarias o privativas de la libertad, de conformidad con lo
dispuesto por los artículos 52 y 53 del Decreto 2591 de 1991.

Resulta entonces, que la figura jurídica del desacato se traduce en una medida
de carácter coercitivo y sancionatorio con que cuenta el juez de conocimiento
de la tutela, en ejercicio de su potestad disciplinaria, para sancionar con
arresto y multa, a quie n desatienda las órdenes o resoluciones judiciales que
se han expedido para hacer efectivo la protección de derechos fundamentales,
a favor de quien o quienes han solicitado su amparo.”

En ese orden de ideas, se advierte que la accionada cumplió con lo dispuesto
en el fallo de tutela proferido por este despacho, de forma tal que no resulta
procedente aperturar incidente de desacato en su contra.

Así las cosas, el d espacho se abstendrá de dar apertura a l incidente de
desacato promovido por la parte actora en razón a la inexistencia de
incumplimiento de la decisión proferida por este despacho. En consecuencia
y de conformidad con la parte motiva precedente, se ordenará el archivo del
incidente de desacato.

En mérito de lo expuesto, el Juzgado 124 Penal Municipal con Función
de Conocimiento de Bogotá D.C., administrando justicia en nombre de la
República y por autoridad de la ley,

RESUELVE:

PRIMERO: ABSTENERSE de dar apertura a l incidente de desacato
promovido por SOL KATHERINE NOREÑA ARIAS  en contra de l
Representante Legal de BANCOLOMBIA S.A.


JUZGADO 124 PENAL MUNICIPAL FUNCIÓN
CONOCIMIENTO DE BOGOTÁ D.C.
<EMAIL>




Página 3 de 3

SEGUNDO: ARCHIVESE el presente Incidente de Desacato.

TERCERO: NOTIFICAR esta providencia  a las partes e interesados, de
conformidad a lo establecido en el artículo 30 del Decreto 2591 de 1991.

COMUNÍQUESE Y CÚMPLASE.






JAIME JAIR PIRABAN GUARNIZO
JUEZ


"""

prompt = f"""
Eres un abogado que procesa información de documentos judiciales detectando elementos importantes en las demandas. A continuación se te da un listado de los elementos a encontrar dentro del documento "Review_text," el cual es un formato de documento legal:

**Elementos a identificar:**

1. **Fecha**: Es la fecha en que se presenta el formato de documento legal. Es la primera fecha que aparece en el texto.
   - Formato: "dd/mm/aaaa."
   - Regularmente aparece junto con "Ciudad."

2. **Juzgado**: Nombre completo del juzgado que emite la decisión.
   - Suele aparecer en el encabezado o en las primeras líneas del texto.

3. **Radicado**: Es un número de al menos 18 dígitos, con o sin guiones "-."
   - Puede aparecer como: "Radicación," "RADICACIÓN," "Rad," "RAD," "Radicado No.," "RADICADO No.," "Radicación No.," "RADICACIÓN No.," "RAD No." o "Rad No."
   - Elimina los espacios y guiones para el resultado final. Ejemplo: "1000 1234-2024" → "100012342024."
   - Si no se encuentra, devuelve "Radicado": "unknown."

4. **Correo_juzgado**: Es una lista que contiene todos los correos electrónicos presentes en el documento.
   - Busca cualquier string con "@" en todo el documento.
   - Asegúrate de no repetir correos en la lista.
   - Si no hay correos, devuelve "Correo_juzgado": "unknown."

5. **Accionante**: Es la persona o empresa que solicita la acción de tutela.
   - Si contiene "CC" o "NIT," elimínalos.

6. **Accionado**: Es la persona o empresa demandada.
   - Asegúrate de no confundir el "Accionante" con el "Accionado."

7. **Decision**: Es la decisión tomada por el juzgado en relación al desacato. Debe ser una de las siguientes categorías:
   - `"Abierto"`: El incidente de desacato sigue en trámite, requiriendo al accionado para que cumpla con lo ordenado en la tutela.
   - `"Cerrado"`: Se archiva o finaliza el proceso de desacato porque no hay incumplimiento o ya no es necesario continuar.
   - `"Sanción"`: Se impone una multa o arresto por incumplimiento de la tutela.
   - `"Sanción_revocada"`: Se revoca una sanción previamente impuesta.
   - `"Sanción_inaplicada"`: Se decide no hacer efectiva una sanción.
   - `"Sanción_confirmada"`: Se confirma una sanción o se ratifica la validez de la tutela tras una apelación.
   - `"unknown"`: No hay información suficiente para determinar el estado.

   **Instrucciones para determinar "Decision":**
   - "Abierto": El juzgado decide iniciar o continuar con el trámite del incidente de desacato, requiriendo al accionado para que cumpla con lo ordenado en la tutela o para que justifique su actuación. Puedes identificar las siguientes palabras clave: “requiere,” “requerir,” “requiérase,” “incidente,” “desacato,” “requerimiento,” “requerimiento previo,” “requiere previo,” “apertura incidente de desacato,” “aperturar.”
     - Ejemplo: "Se requiere a la accionada para que cumpla en el término establecido."
     - Resultado: "Abierto."

   - "Cerrado": El juzgado determina que no hay incumplimiento del fallo de tutela o que el proceso de desacato no debe continuar, archivando el incidente y dando por finalizada la actuación. Puedes identificar las siguientes palabras clave: "archiva," "termina," "cerrar," "cierra," "finaliza," "abstenerse," "archivar," "cierra y archiva."
     - Ejemplo: "El despacho archiva el incidente debido al cumplimiento del fallo."
     - Resultado: "Cerrado."

   - "Sanción": El juzgado concluye que el accionado incumplió la orden de tutela y le impone una sanción, que puede ser una multa o arresto. Puedes buscar palabras clave relacionadas con sanciones: "sanciona," "sancionar," "sancionar por desacato," "multa," "ordenar ejecutar," "imponer multa."
     - Ejemplo: "El despacho sanciona al accionado con una multa por incumplimiento."
     - Resultado: "Sanción."

   - "Sanción_revocada": Una sanción previamente impuesta al accionado es revocada o anulada por el juzgado, ya sea por cumplimiento posterior o por una reconsideración de la decisión. Puedes buscar palabras clave como: "revoca sanción," "revocar decisión," "revocar la sanción."
     - Ejemplo: "Se revoca la sanción previamente impuesta."
     - Resultado: "Sanción_revocada."

   - "Sanción_inaplicada": Aunque inicialmente se consideró la posibilidad de sancionar al accionado, el juzgado decide no hacer efectiva la sanción, ya sea porque el cumplimiento se dio tardíamente o porque se considera injustificada la sanción. Puedes identificar palabras clave como: "inaplica sanción," "no aplicar," "no hacer efectiva," "inejecuta."
     - Ejemplo: "Se decide no hacer efectiva la sanción impuesta."
     - Resultado: "Sanción_inaplicada."

   - "sancion_confirmada": Se confirma una sanción o se ratifica la validez de la tutela tras una apelación.
     - Ejemplo: Confirmar el auto del 2 de septiembre de 2024, por las razones esgrimidas en la parte motiva de esta decisión.
     - Resultado: "sancion_confirmada"

   **Notas importantes:**
   - Evalúa las palabras clave únicamente en la sección "Resuelve" o sus equivalentes ("DECISION," "CONCLUSION," "FALLA").
   - Si no se encuentra ninguna palabra clave relevante, devuelve: "Decision": "unknown."
   - Si aparecen múltiples estados (por ejemplo, "Sanción" y "Sanción_revocada"), selecciona el más reciente o conclusivo según el contexto del "Resuelve."
   - No busques solamente la palabra clave, sino el contexto, por ejemplo en "se abstiene de sancionar", a pesar de que sale sancionar, por el abstiene el resultado debe ser Sancion_inaplicada

8. **Término**: Es el plazo (en horas o días) que el juzgado otorga para cumplir la orden de tutela.
   - **Dónde buscar**: Sección "Resuelve" o párrafos inmediatamente anteriores o posteriores.
   - **Formato esperado**: `"X horas"` o `"X días"` (sin abreviaturas).
   - **Ejemplo**: `"En el término de 48 horas"` → `"Término": "48 horas"`
   - **Reglas de fallback**:
     - Si no se menciona explícitamente, devolver: `"Término": ""`.

---

**Ejemplo con texto:**

Texto:
{text_ex1}

**Salida esperada:**

"Fecha": "12/12/2024",
"Radicado": "11001400912420240026100",
"Correo_juzgado": ["<EMAIL>"],
"Juzgado": "JUZGADO 124 PENAL MUNICIPAL CON FUNCION DE CONOCIMIENTO DE BOGOTÁ D.C.",
"Accionante": "SOL KATHERINE NOREÑA ARIAS",
"Accionado": ["BANCOLOMBIA S.A."],
"Decision": "Cerrado",
"Término": ""

"""

class TutelaDesacatoProcessor(DocumentProcessor):
    def __init__(self, openai_client: OpenAIClient,  langchain_client: LangChainClient):
        self.openai_client = openai_client
        self.langchain_client = langchain_client

    async def process(self, file: Optional[UploadFile], data: Optional[str] = None) -> dict:
        if not file and not data:
            raise HTTPException(status_code=400, detail="The CV action requires a file or URL")
        result = await self.process_tutela(file, data)
        return result

    async def process_tutela(self, file: UploadFile, data: Optional[str] = None) -> dict:
        """
        Process a tutela document. If 'data' is a URL to a PDF, it downloads and processes it.
        If 'file' is provided, it reads the uploaded file.

        Raises:
            HTTPException: For various processing errors
            asyncio.CancelledError: If the task is cancelled
        """
        # Track temporary files for cleanup in case of cancellation
        temp_files = []

        # Crear un directorio temporal para almacenar los archivos
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                if data:
                    # Descargar el archivo desde la URL proporcionada en 'data'
                    url = data.strip()
                    response = requests.get(url)
                    if response.status_code != 200:
                        raise HTTPException(status_code=400, detail="No se pudo descargar el archivo desde la URL proporcionada.")
                    # Obtener el nombre del archivo desde la URL
                    filename = url.split("/")[-1].split("?")[0]
                    file_extension = filename.split(".")[-1].lower()

                    pdf_path = os.path.join(temp_dir, filename)
                    with open(pdf_path, "wb") as f:
                        f.write(response.content)
                    # Track the temporary file
                    temp_files.append(pdf_path)

                else:
                    # Usar el archivo subido
                    filename = file.filename
                    file_extension = filename.split(".")[-1].lower()

                    pdf_path = os.path.join(temp_dir, filename)
                    file_content = await file.read()
                    with open(pdf_path, "wb") as f:
                        f.write(file_content)
                    # Track the temporary file
                    temp_files.append(pdf_path)

                if file_extension == 'pdf':
                    # Guardar el PDF en el directorio temporal
                    pdf_path = os.path.join(temp_dir, filename)

                    CV_tot_text = await get_text_from_pdf(pdf_path)

                    os.remove(pdf_path)


                else:
                    raise HTTPException(status_code=400, detail="El archivo debe ser un PDF.")

                CV_data = await self.get_structured_data(CV_tot_text, TutelaDesacatoDocument)

                return CV_data

            except asyncio.CancelledError:
                # Clean up any temporary files that might still exist
                for temp_file in temp_files:
                    if os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except Exception as cleanup_error:
                            print(f"Error cleaning up file {temp_file}: {cleanup_error}")
                # Re-raise to propagate the cancellation
                raise

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Error al procesar el CV: {str(e)}")


    async def get_structured_data(self, tutela_text: str, tutela_model: type[BaseModel]):
        """
        Extract structured data from tutela text using language model.

        Args:
            tutela_text (str): Processed tutela text
            tutela_model (type[BaseModel]): Pydantic model to structure the data

        Returns:
            dict: Structured tutela data

        Raises:
            HTTPException: Passed through from LangChainClient
        """
        data = f"Here is the Review_text: {tutela_text}"
        correos_cendoj = await self.extraer_correos_cendoj(tutela_text)

        # Call the langchain_client's get_structured_data method
        # The helper function handles all validation, retries, and error cases
        response = await self.langchain_client.get_structured_data(tutela_model, data, prompt)

        # Extract the response content and add the correos_cendoj
        try:
            response_data = response['response']
            result = {**response_data, "correo": correos_cendoj}
            return result
        except Exception as e:
            print(f"Error extracting response data: {e}")
            return response


    async def extraer_correos_cendoj(self, texto: str) -> list:
        """
        Extrae todos los correos únicos que terminan en '@cendoj.ramajudicial.gov.co' de un texto.

        Parámetros:
            texto (str): El texto completo donde se buscarán los correos.

        Retorna:
            list: Lista de correos únicos ordenados alfabéticamente.
        """
        patron_correos = r'[a-zA-Z0-9._%+-]+@cendoj\.ramajudicial\.gov\.co'
        correos = re.findall(patron_correos, texto)
        correos_unicos = sorted(set(correos))
        return correos_unicos