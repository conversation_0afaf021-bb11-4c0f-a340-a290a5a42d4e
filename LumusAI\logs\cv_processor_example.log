2025-05-16 01:40:15,338 - INFO - Starting CV processor example
2025-05-16 01:40:15,338 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 01:40:15,339 - INFO - Initialized Lumus API Client with base URL: http://localhost:8000
2025-05-16 01:40:15,339 - INFO - Calling the CV processor API...
2025-05-16 01:40:15,339 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 01:40:15,339 - INFO - Sending request to endpoint: http://localhost:8000/process/cv
2025-05-16 01:40:15,340 - INFO - Sending CV file to API...
2025-05-16 01:40:16,652 - INFO - Received response with status: 404
2025-05-16 01:40:16,653 - ERROR - API request failed with status 404: {"detail":"Not Found"}
2025-05-16 01:40:16,653 - ERROR - Unexpected error processing CV: API request failed with status 404: {"detail":"Not Found"}
2025-05-16 01:40:16,654 - ERROR - Error processing CV: API request failed with status 404: {"detail":"Not Found"}
2025-05-16 01:41:11,290 - INFO - Starting CV processor example
2025-05-16 01:41:11,291 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 01:41:11,291 - INFO - Initialized Lumus API Client with base URL: http://localhost:8000
2025-05-16 01:41:11,291 - INFO - Calling the CV processor API...
2025-05-16 01:41:11,291 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 01:41:11,292 - INFO - Sending request to endpoint: http://localhost:8000/process
2025-05-16 01:41:11,292 - INFO - Sending CV file to API...
2025-05-16 01:43:07,695 - INFO - Received response with status: 503
2025-05-16 01:43:07,696 - ERROR - API request failed with status 503: {"error":"Could not connect to the OpenAI API","details":"Connection error."}
2025-05-16 01:43:07,696 - ERROR - Unexpected error processing CV: API request failed with status 503: {"error":"Could not connect to the OpenAI API","details":"Connection error."}
2025-05-16 01:43:07,696 - ERROR - Error processing CV: API request failed with status 503: {"error":"Could not connect to the OpenAI API","details":"Connection error."}
2025-05-16 01:54:38,053 - INFO - Starting CV processor example
2025-05-16 01:54:38,053 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 01:54:38,053 - INFO - Initialized Lumus API Client with base URL: http://localhost:8000
2025-05-16 01:54:38,054 - INFO - Calling the CV processor API...
2025-05-16 01:54:38,054 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 01:54:38,054 - INFO - Sending request to endpoint: http://localhost:8000/process
2025-05-16 01:54:38,054 - INFO - Sending CV file to API...
2025-05-16 01:55:35,058 - INFO - Received response with status: 200
2025-05-16 01:55:35,058 - INFO - Successfully processed CV
2025-05-16 01:55:35,076 - INFO - Successfully processed CV. Result contains 1 fields
2025-05-16 02:00:40,489 - INFO - Starting CV processor example
2025-05-16 02:00:40,490 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 02:00:40,490 - INFO - Initialized Lumus API Client with base URL: http://localhost:8000
2025-05-16 02:00:40,490 - INFO - Calling the CV processor API...
2025-05-16 02:00:40,490 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 02:00:40,490 - INFO - Sending request to endpoint: http://localhost:8000/process
2025-05-16 02:00:40,491 - INFO - Sending CV file to API...
2025-05-16 02:08:31,253 - INFO - Starting CV processor example
2025-05-16 02:08:31,254 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 02:08:31,254 - INFO - Initialized Lumus API Client with base URL: http://localhost:8000
2025-05-16 02:08:31,254 - INFO - Calling the CV processor API...
2025-05-16 02:08:31,254 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 02:08:31,254 - INFO - Sending request to endpoint: http://localhost:8000/process
2025-05-16 02:08:31,255 - INFO - Sending CV file to API...
2025-05-16 02:09:20,514 - INFO - Received response with status: 200
2025-05-16 02:09:20,515 - INFO - Successfully processed CV
2025-05-16 02:09:20,525 - INFO - Successfully processed CV. Result contains 1 fields
2025-05-16 02:10:52,875 - INFO - Starting CV processor example
2025-05-16 02:10:52,875 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 02:10:52,876 - INFO - Initialized Lumus API Client with base URL: http://localhost:8000
2025-05-16 02:10:52,876 - INFO - Calling the CV processor API...
2025-05-16 02:10:52,876 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 02:10:52,876 - INFO - Sending request to endpoint: http://localhost:8000/process
2025-05-16 02:10:52,877 - INFO - Sending CV file to API...
2025-05-16 02:11:49,198 - INFO - Received response with status: 200
2025-05-16 02:11:49,199 - INFO - Successfully processed CV
2025-05-16 02:11:49,200 - INFO - Reading PDF content for validation...
2025-05-16 02:11:49,404 - INFO - Validating and fixing CV data...
2025-05-16 02:11:49,422 - INFO - Successfully processed and fixed CV. Result contains 1 fields
2025-05-16 02:14:54,442 - INFO - Starting CV processor example
2025-05-16 02:14:54,442 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 02:14:54,442 - INFO - Initialized Lumus API Client with base URL: http://localhost:8000
2025-05-16 02:14:54,443 - INFO - Calling the CV processor API...
2025-05-16 02:14:54,443 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 02:14:54,443 - INFO - Sending request to endpoint: http://localhost:8000/process
2025-05-16 02:14:54,443 - INFO - Sending CV file to API...
2025-05-16 02:15:47,887 - INFO - Received response with status: 200
2025-05-16 02:15:47,888 - INFO - Successfully processed CV
2025-05-16 02:15:47,888 - INFO - Reading PDF content for validation...
2025-05-16 02:15:48,090 - INFO - Validating and fixing CV data...
2025-05-16 02:15:48,104 - INFO - Successfully processed and fixed CV. Result contains 1 fields
2025-05-16 02:15:48,782 - INFO - Starting CV processor example
2025-05-16 02:15:48,782 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 02:15:48,783 - INFO - Initialized Lumus API Client with base URL: http://localhost:8000
2025-05-16 02:15:48,783 - INFO - Calling the CV processor API...
2025-05-16 02:15:48,783 - INFO - Processing CV file: data/UMA SEKARAN - BSA (1).pdf
2025-05-16 02:15:48,783 - INFO - Sending request to endpoint: http://localhost:8000/process
2025-05-16 02:15:48,784 - INFO - Sending CV file to API...
2025-05-16 02:16:49,703 - INFO - Received response with status: 200
2025-05-16 02:16:49,704 - INFO - Successfully processed CV
2025-05-16 02:16:49,704 - INFO - Reading PDF content for validation...
2025-05-16 02:16:49,904 - INFO - Validating and fixing CV data...
2025-05-16 02:16:49,926 - INFO - Successfully processed and fixed CV. Result contains 1 fields
