import time
import asyncio
from fastapi import APIRout<PERSON>, Request, BackgroundTasks

router = APIRouter()

# Function to clean up stale tasks
def cleanup_stale_tasks(active_tasks, logger, max_age_seconds=3600):
    """Remove tasks that have been running for too long or are in an invalid state."""
    current_time = time.time()
    tasks_to_remove = []
    
    for task, metadata in active_tasks.items():
        # Check if task is done or cancelled but still in the list
        if task.done() or task.cancelled():
            tasks_to_remove.append(task)
            continue
            
        # Check if task has been running for too long
        start_time = metadata.get("start_time", current_time)
        task_age = current_time - start_time
        
        if task_age > max_age_seconds:
            logger.warning(f"Task {metadata.get('task_id', id(task))} ({metadata['action']}) has been running for {task_age:.2f}s, which exceeds the maximum allowed time of {max_age_seconds}s")
            tasks_to_remove.append(task)
    
    # Remove the identified tasks
    removed_count = 0
    for task in tasks_to_remove:
        if task in active_tasks:
            metadata = active_tasks.pop(task, {"action": "unknown"})
            logger.info(f"Removed stale task {metadata.get('task_id', id(task))} ({metadata.get('action', 'unknown')})")
            removed_count += 1
    
    if removed_count > 0:
        logger.warning(f"Cleaned up {removed_count} stale tasks")
    
    return removed_count

@router.get("/maintenance/tasks", summary="Task Maintenance")
async def task_maintenance(request: Request, background_tasks: BackgroundTasks):
    """
    Performs maintenance on the task queue:
    - Removes completed or cancelled tasks that weren't properly cleaned up
    - Identifies and logs long-running tasks
    
    This endpoint is separate from health checks to keep monitoring and maintenance separate.
    """
    active_tasks = request.app.state.active_tasks
    logger = request.app.state.logger
    
    # Schedule cleanup in the background
    background_tasks.add_task(cleanup_stale_tasks, active_tasks, logger)
    
    # Count tasks by status
    total_tasks = len(active_tasks)
    done_tasks = sum(1 for task in active_tasks if task.done())
    cancelled_tasks = sum(1 for task in active_tasks if task.cancelled())
    running_tasks = total_tasks - done_tasks - cancelled_tasks
    
    # Get information about running tasks
    running_task_info = []
    for task, metadata in active_tasks.items():
        if not task.done() and not task.cancelled():
            start_time = metadata.get("start_time", time.time())
            task_age = time.time() - start_time
            
            running_task_info.append({
                "task_id": metadata.get("task_id", id(task)),
                "action": metadata["action"],
                "running_time_seconds": round(task_age, 2),
                "file_name": metadata.get("file_name", "Unknown")
            })
    
    return {
        "status": "maintenance_scheduled",
        "message": "Task maintenance has been scheduled",
        "task_counts": {
            "total": total_tasks,
            "running": running_tasks,
            "done": done_tasks,
            "cancelled": cancelled_tasks
        },
        "running_tasks": running_task_info
    }
