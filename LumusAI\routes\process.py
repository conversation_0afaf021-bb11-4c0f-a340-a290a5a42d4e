import asyncio
import time
from fastapi import APIRouter, HTTPException, UploadFile, Form, File, Request

from services.processors.smarthr.cv_processor_test import CVProcessorTest
from services.processors.papirus.tutela_contestacion_processor import TutelaContestacionProcessor
from services.processors.papirus.tutela_fallo_processor import TutelaFalloProcessor
from services.processors.papirus.tutela_desacato_processor import TutelaDesacatoProcessor
from services.processors.papirus.tutela_correo_processor import TutelaCorreoProcessor
from services.processors.facturius.invoice_processor_test import InvoiceProcessorTest

router = APIRouter()


def get_processor_mapping(openai_client, langchain_client):
    """
    Returns a mapping of action names to their corresponding processor
    instances.
    """

    return {
        #"invoice": InvoiceProcessor(openai_client, langchain_client),
        "invoice": InvoiceProcessorTest(openai_client, langchain_client),
        "cv": CVProcessorTest(openai_client, langchain_client),
        "tutela_contestacion": TutelaContestacionProcessor(openai_client, langchain_client),
        "tutela_correo_contestacion": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_fallo": TutelaFalloProcessor(openai_client, langchain_client),
        "tutela_correo_fallo": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_desacato": TutelaDesacatoProcessor(openai_client, langchain_client),
        "tutela_correo_desacato": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_correo": TutelaCorreoProcessor(openai_client, langchain_client),
    }


@router.post("/process", summary="Process a document")
async def process_request(
    request: Request,
    action: str = Form(..., description="Document type (e.g., invoice, cv)"),
    file: UploadFile = File(None, description="File for processing"),
    data: str = Form(None, description="Text or URL to process")
):
    """
    Process a document based on the specified action and return information in
    JSON format.

    - **Parameters**:
      - **action**: Document type or action name (e.g., 'invoice', 'cv').
      - **file**: File to process if no text is provided.
      - **data**: Text or URL to process if no file is provided.

    - **Constraints**:
      Only one of `file` or `data` can be provided. If both are given, the
      request fails.

    - **Returns**:
      A JSON object with the processed information.

    Example using cURL:
    ```
    curl -X POST "http://localhost:8000/process" \
      -F "action=invoice" \
      -F "file=@/path/to/file.pdf"
    ```
    """

    logger = request.app.state.logger
    logger.info(f"Process request initiated with action: {action}")

    if not file and not data:
        logger.warning("No file or data provided.")
        raise HTTPException(status_code=400, detail="A file or text is required.")
    if file and data:
        logger.warning("Both file and data provided. Only one allowed.")
        raise HTTPException(status_code=400, detail="Send either a text or a file, not both.")

    action = action.lower()

    openai_client = request.app.state.openai_client
    langchain_client = request.app.state.langchain_client

    processor_mapping = get_processor_mapping(openai_client, langchain_client)
    processor = processor_mapping.get(action)
    if not processor:
        raise HTTPException(status_code=400, detail="Invalid Action.")

    semaphore = request.app.state.semaphore
    active_tasks = request.app.state.active_tasks

    async def handle_request_with_semaphore(processor, file, data):
        """
        Handle request using a semaphore to limit concurrency.
        """
        async with semaphore:
            try:
                result = await processor.process(file, data)
                return result
            except asyncio.CancelledError:
                logger.warning("Task was cancelled during processing")
                raise  # Re-raise to ensure proper cleanup
            except Exception as e:
                logger.error(f"Error processing action: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail=f"Error processing the request: {str(e)}")

    # Create a unique task ID for tracking
    task_id = f"{action}-{time.time()}-{id(processor)}"

    # Create the task with proper error handling
    task = asyncio.create_task(handle_request_with_semaphore(processor, file, data))

    # Store task with metadata for better tracking
    active_tasks[task] = {
        "action": action,
        "task_id": task_id,
        "start_time": time.time(),
        "file_name": getattr(file, "filename", None) or str(data)[:30] if data else "No file"
    }

    # Add done callback to ensure task is removed even if the main code path fails
    def task_done_callback(completed_task):
        try:
            # Check if task is still in active_tasks (might have been removed already)
            if completed_task in active_tasks:
                task_info = active_tasks.pop(completed_task, {"action": "unknown"})
                duration = time.time() - task_info.get("start_time", time.time())
                logger.info(f"Task {task_info.get('task_id', 'unknown')} ({task_info['action']}) completed in {duration:.2f}s")

                # Check for exceptions
                if completed_task.exception():
                    error = completed_task.exception()
                    logger.error(f"Task {task_info.get('task_id', 'unknown')} failed with error: {error}")
        except Exception as e:
            logger.error(f"Error in task cleanup callback: {e}")

    # Add the callback
    task.add_done_callback(task_done_callback)

    try:
        # Wait for the task to complete
        result = await task
        return result
    except asyncio.CancelledError:
        # Handle cancellation gracefully
        logger.warning(f"Task {task_id} was cancelled")
        raise HTTPException(status_code=499, detail="Request cancelled")
    except Exception as e:
        # Log any unexpected exceptions
        logger.error(f"Unexpected error in task {task_id}: {str(e)}")
        raise
    finally:
        # This is a backup to ensure the task is removed from active_tasks
        # The done_callback should handle this in most cases
        if task in active_tasks:
            active_tasks.pop(task, None)
