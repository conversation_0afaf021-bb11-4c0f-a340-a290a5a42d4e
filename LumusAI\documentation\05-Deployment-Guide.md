# Deployment Guide
## LumusAI - Intelligent Document Processing Service

**Document Version:** 1.0  
**Date:** December 2024  
**DevOps Team:** Development Team  
**Project:** LumusAI  

---

## Table of Contents

1. [Prerequisites](#1-prerequisites)
2. [Environment Setup](#2-environment-setup)
3. [Docker Deployment](#3-docker-deployment)
4. [Local Development](#4-local-development)
5. [Production Deployment](#5-production-deployment)
6. [Monitoring and Logging](#6-monitoring-and-logging)
7. [Troubleshooting](#7-troubleshooting)
8. [Maintenance](#8-maintenance)

---

## 1. Prerequisites

### 1.1 System Requirements

**Minimum Requirements:**
- **CPU:** 2 cores
- **RAM:** 4GB
- **Storage:** 10GB available space
- **Network:** Internet connectivity for AI model access

**Recommended Requirements:**
- **CPU:** 4+ cores
- **RAM:** 8GB+
- **Storage:** 20GB+ SSD
- **Network:** High-speed internet connection

### 1.2 Software Dependencies

**Required Software:**
- Docker 20.10+ and Docker Compose 2.0+
- Git for source code management
- Text editor for configuration files

**For Local Development:**
- Python 3.12.7
- pip package manager
- Virtual environment tools (venv, conda, etc.)

### 1.3 External Service Requirements

**OpenAI/Azure OpenAI:**
- Valid API key with sufficient quota
- Access to GPT-4 or compatible models
- Network access to OpenAI endpoints

**Optional Services:**
- Monitoring tools (Prometheus, Grafana)
- Log aggregation (ELK stack, Fluentd)
- Load balancer (nginx, HAProxy)

## 2. Environment Setup

### 2.1 Environment Variables

**Required Configuration:**
```env
# OpenAI API Configuration
API_KEY=your_openai_api_key_here
API_VERSION=2023-05-15
AZURE_ENDPOINT=https://your-resource.openai.azure.com/
MODEL=gpt-4-vision-preview

# Application Configuration
MAX_CONCURRENT_TASKS=4
ROOT_PATH=""
```

**Optional Configuration:**
```env
# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Performance Tuning
WORKER_TIMEOUT=300
MAX_FILE_SIZE=52428800  # 50MB in bytes

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
```

### 2.2 Configuration Files

**Create .env file:**
```bash
cp .env.example .env
# Edit .env with your specific configuration
```

**Docker Compose Configuration:**
```yaml
version: '3.8'
services:
  lumusai:
    build: .
    ports:
      - "8000:8000"
    environment:
      - API_KEY=${API_KEY}
      - API_VERSION=${API_VERSION}
      - AZURE_ENDPOINT=${AZURE_ENDPOINT}
      - MODEL=${MODEL}
      - MAX_CONCURRENT_TASKS=${MAX_CONCURRENT_TASKS:-4}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### 2.3 Security Configuration

**API Key Management:**
- Store API keys securely (environment variables, secrets management)
- Rotate API keys regularly
- Monitor API usage and quotas

**Network Security:**
- Configure firewall rules
- Use HTTPS in production
- Implement reverse proxy if needed

## 3. Docker Deployment

### 3.1 Quick Start with Docker

**Step 1: Clone Repository**
```bash
git clone <repository-url>
cd LumusAI
```

**Step 2: Configure Environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

**Step 3: Build and Run**
```bash
docker-compose up --build
```

**Step 4: Verify Deployment**
```bash
curl http://localhost:8000/health
```

### 3.2 Docker Build Process

**Manual Docker Build:**
```bash
# Build image
docker build -t lumusai:latest .

# Run container
docker run -d \
  --name lumusai \
  -p 8000:8000 \
  --env-file .env \
  -v $(pwd)/logs:/app/logs \
  lumusai:latest
```

**Multi-stage Build (Production):**
```dockerfile
# Production Dockerfile
FROM python:3.12.7-alpine as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

FROM python:3.12.7-alpine
WORKDIR /app
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY . .
EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 3.3 Docker Compose for Production

**Production docker-compose.yml:**
```yaml
version: '3.8'
services:
  lumusai:
    image: lumusai:latest
    ports:
      - "8000:8000"
    environment:
      - API_KEY=${API_KEY}
      - API_VERSION=${API_VERSION}
      - AZURE_ENDPOINT=${AZURE_ENDPOINT}
      - MODEL=${MODEL}
      - MAX_CONCURRENT_TASKS=8
    volumes:
      - ./logs:/app/logs
      - /tmp:/tmp
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'
        reservations:
          memory: 2G
          cpus: '1'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - lumusai
    restart: unless-stopped
```

## 4. Local Development

### 4.1 Python Virtual Environment

**Setup Virtual Environment:**
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Linux/Mac:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

**Development Server:**
```bash
# Run development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Or with auto-reload
python -m uvicorn main:app --reload
```

### 4.2 Development Configuration

**Development .env:**
```env
API_KEY=your_dev_api_key
API_VERSION=2023-05-15
AZURE_ENDPOINT=https://your-dev-resource.openai.azure.com/
MODEL=gpt-4-vision-preview
MAX_CONCURRENT_TASKS=2
LOG_LEVEL=DEBUG
```

**IDE Configuration:**
- Configure Python interpreter to use virtual environment
- Set up debugging configurations
- Install recommended extensions (Python, Docker)

### 4.3 Testing Setup

**Run Tests:**
```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest tests/

# Run with coverage
pytest --cov=. tests/
```

**Test Configuration:**
```python
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
```

## 5. Production Deployment

### 5.1 Production Environment Setup

**Server Preparation:**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create application directory
sudo mkdir -p /opt/lumusai
sudo chown $USER:$USER /opt/lumusai
```

**Production Deployment:**
```bash
# Clone repository
cd /opt/lumusai
git clone <repository-url> .

# Configure environment
cp .env.example .env
# Edit .env with production values

# Deploy with Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# Verify deployment
curl http://localhost:8000/health
```

### 5.2 Reverse Proxy Configuration

**Nginx Configuration:**
```nginx
upstream lumusai {
    server localhost:8000;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    client_max_body_size 50M;
    
    location / {
        proxy_pass http://lumusai;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeout settings for long-running requests
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    location /health {
        proxy_pass http://lumusai/health;
        access_log off;
    }
}
```

### 5.3 SSL/TLS Configuration

**Let's Encrypt Setup:**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 5.4 Process Management

**Systemd Service:**
```ini
# /etc/systemd/system/lumusai.service
[Unit]
Description=LumusAI Document Processing Service
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/lumusai
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
```

**Enable and Start Service:**
```bash
sudo systemctl enable lumusai
sudo systemctl start lumusai
sudo systemctl status lumusai
```

## 6. Monitoring and Logging

### 6.1 Application Monitoring

**Health Check Monitoring:**
```bash
# Simple health check script
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health)
if [ $response != "200" ]; then
    echo "LumusAI health check failed: $response"
    # Send alert or restart service
fi
```

**Resource Monitoring:**
```bash
# Monitor Docker container resources
docker stats lumusai

# Monitor system resources
htop
iostat -x 1
```

### 6.2 Logging Configuration

**Log Rotation:**
```bash
# /etc/logrotate.d/lumusai
/opt/lumusai/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker-compose -f /opt/lumusai/docker-compose.prod.yml restart lumusai
    endscript
}
```

**Centralized Logging:**
```yaml
# Add to docker-compose.yml
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 6.3 Performance Monitoring

**Metrics Collection:**
- Response time monitoring
- Error rate tracking
- Resource utilization
- API usage statistics

**Alerting Rules:**
- High error rates (>5%)
- Slow response times (>60s)
- High resource usage (>80%)
- Service unavailability

## 7. Troubleshooting

### 7.1 Common Issues

**Service Won't Start:**
```bash
# Check Docker logs
docker-compose logs lumusai

# Check system resources
df -h
free -h

# Check port availability
netstat -tlnp | grep 8000
```

**API Errors:**
```bash
# Check API key configuration
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@test.pdf"

# Check OpenAI connectivity
curl -H "Authorization: Bearer $API_KEY" \
     https://api.openai.com/v1/models
```

**Performance Issues:**
```bash
# Monitor resource usage
docker stats

# Check concurrent tasks
curl http://localhost:8000/health

# Review logs for bottlenecks
tail -f logs/app.log
```

### 7.2 Debug Mode

**Enable Debug Logging:**
```env
LOG_LEVEL=DEBUG
```

**Debug Container:**
```bash
# Run container in debug mode
docker run -it --rm \
  --env-file .env \
  lumusai:latest \
  /bin/sh
```

### 7.3 Recovery Procedures

**Service Recovery:**
```bash
# Restart service
docker-compose restart lumusai

# Full rebuild
docker-compose down
docker-compose up --build -d

# Clear cache and restart
docker system prune -f
docker-compose up --build -d
```

## 8. Maintenance

### 8.1 Regular Maintenance Tasks

**Daily:**
- Monitor service health
- Check error logs
- Verify API functionality

**Weekly:**
- Review performance metrics
- Update dependencies if needed
- Clean up old logs

**Monthly:**
- Security updates
- Backup configuration
- Review and optimize performance

### 8.2 Update Procedures

**Application Updates:**
```bash
# Pull latest code
git pull origin main

# Rebuild and deploy
docker-compose down
docker-compose up --build -d

# Verify deployment
curl http://localhost:8000/health
```

**Dependency Updates:**
```bash
# Update requirements
pip-compile requirements.in

# Test updates in development
# Deploy to production after testing
```

### 8.3 Backup and Recovery

**Configuration Backup:**
```bash
# Backup configuration files
tar -czf lumusai-config-$(date +%Y%m%d).tar.gz \
  .env docker-compose.yml nginx.conf

# Store backups securely
```

**Disaster Recovery:**
```bash
# Restore from backup
tar -xzf lumusai-config-YYYYMMDD.tar.gz

# Redeploy service
docker-compose up -d
```

---

**Document Control:**
- **Version:** 1.0
- **Status:** Final
- **Last Updated:** December 2024
- **Next Review:** Upon deployment changes
